{"name": "xosportshub", "version": "1.0.0", "description": "Digital content marketplace platform for sports training", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seeders/index.js", "test:s3": "node scripts/validate-s3-config.js", "verify:s3-large-uploads": "node scripts/verify-s3-large-uploads.js"}, "keywords": ["sports", "training", "marketplace", "content", "ecommerce"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"aws-sdk": "^2.1386.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "ffmpeg-static": "^5.2.0", "firebase-admin": "^13.4.0", "fluent-ffmpeg": "^2.1.3", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.2.0", "morgan": "^1.10.0", "multer": "^2.0.0", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.2", "pdf-lib": "^1.17.1", "pdfkit": "^0.13.0", "stripe": "^12.6.0", "twilio": "^5.6.1"}, "devDependencies": {"nodemon": "^3.1.10"}}