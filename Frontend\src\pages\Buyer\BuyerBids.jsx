import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { getUserBids, cancelBid } from "../../redux/slices/bidSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import LoadingSkeleton, {
  TableRowSkeleton,
} from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import Pagination from "../../components/common/Pagination";
import {
  FaGavel,
  FaSync,
  FaTimes,
  FaCreditCard,
  FaClock,
  FaCheck,
} from "react-icons/fa";
import { FiEye } from "react-icons/fi";
import Table from "../../components/common/Table";
import "../../styles/BuyerBids.css";
import { getImageUrl, IMAGE_BASE_URL } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";

const BuyerBids = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userBids, isLoading, isError, error, pagination } = useSelector(
    (state) => state.bid
  );

  const [selectedBid, setSelectedBid] = useState(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Fetch bids on component mount or when page changes
  useEffect(() => {
    dispatch(getUserBids({ page: currentPage, limit: itemsPerPage }));
  }, [dispatch, currentPage, itemsPerPage]);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0);
  };

  // Handle retry
  const handleRetry = () => {
    dispatch(getUserBids());
  };

  const handleCancelBid = async (bid) => {
    setIsCancelling(true);
    try {
      const response = await dispatch(cancelBid(bid._id)).unwrap();

      // Show appropriate message based on whether a previous bid was reactivated
      if (response.data?.reactivatedBid) {
        toast.success(
          `Bid cancelled and your previous $${response.data.reactivatedBid.amount} bid is now active`
        );
      } else {
        toast.success("Bid cancelled successfully");
      }

      setShowCancelModal(false);
      setSelectedBid(null);

      // Refresh bids
      dispatch(getUserBids());
    } catch (error) {
      toast.error(error.message || "Failed to cancel bid");
    } finally {
      setIsCancelling(false);
    }
  };

  const handlePayNow = (bid) => {
    // Check if bid has an order and redirect to the existing checkout page
    if (bid.order && bid.order._id) {
      navigate(`/checkout/${bid.order._id}`);
    } else {
      // Fallback to the old checkout page if no order exists
      navigate("/buyer/checkout", {
        state: {
          type: "bid",
          bidId: bid._id,
          amount: bid.amount,
          content: bid.content,
        },
      });
    }
  };

  const openCancelModal = (bid) => {
    setSelectedBid(bid);
    setShowCancelModal(true);
  };

  const closeCancelModal = () => {
    setShowCancelModal(false);
    setSelectedBid(null);
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "Active":
        return "active";
      case "Won":
        return "won";
      case "Lost":
        return "lost";
      case "Cancelled":
        return "status-cancelled";
      case "Outbid":
        return "status-outbid";
      default:
        return "status-default";
    }
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const isAuctionActive = (content) => {
    if (!content?.auctionDetails) return false;

    const now = new Date();
    const startDate = content.auctionDetails.auctionStartDate
      ? new Date(content.auctionDetails.auctionStartDate)
      : null;
    const endDate = content.auctionDetails.auctionEndDate
      ? new Date(content.auctionDetails.auctionEndDate)
      : null;

    return (!startDate || now >= startDate) && (!endDate || now <= endDate);
  };

  // Modified columns: add render for "no" column to show index+1 (with pagination support)
  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
      render: (bid, index) => {
        const safeIndex =
          typeof index === "number" && !isNaN(index) ? index : 0;
        return (currentPage - 1) * itemsPerPage + safeIndex + 1;
      },
    },
    {
      key: "bidId",
      label: "Bid Id",
      className: "bid-id",
      render: (bid) => `#${bid._id?.slice(-6)}`,
    },
    {
      key: "content",
      label: "Videos/Documents",
      className: "video",
      render: (bid) => (
        <div className="content-item">
          <div className="content-image">
            <img
              src={bid.content?.thumbnailUrl ? getImageUrl(bid.content.thumbnailUrl) : getPlaceholderImage(200, 120, "No image")}
              alt={bid.content?.title || "Content"}
            />
          </div>
          <div className="content-info">
            <div className="content-title">
              {bid.content?.title || "Unknown Content"}
            </div>
            <div className="content-coach">
              By {bid.content?.seller?.firstName || "Unknown"}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "date",
      label: "Date",
      className: "date",
      render: (bid) => formatDate(bid.createdAt),
    },
    {
      key: "bidAmount",
      label: "Bid Amount",
      className: "bid-amount",
      render: (bid) => `$${bid.amount?.toFixed(2) || "0.00"}`,
    },
    {
      key: "status",
      label: "Status",
      className: "status",
      render: (bid) => (
        <span className={`status-badge ${getStatusBadgeClass(bid.status)}`}>
          {bid.status}
        </span>
      ),
    },
    {
      key: "auctionStatus",
      label: "Auction",
      className: "auction-status",
      render: (bid) => (
        <span
          className={`auction-indicator auction-statuscss ${isAuctionActive(bid.content) ? "active" : "ended"
            }`}
        >
          <FaClock /> {isAuctionActive(bid.content) ? "Active" : "Ended"}
        </span>
      ),
    },
    {
      key: "action",
      label: "Action",
      className: "action",
      render: (bid) => (
        <div className="action-buttons">
          {bid.status === "Won" ? (
            // Check if payment is completed or expired
            bid.order && bid.order.paymentStatus === "Completed" ? (
              <button className="btn-paid" disabled>
                <FaCheck />
              </button>
            ) : bid.order &&
              (bid.order.paymentStatus === "Expired" ||
                bid.order.status === "Expired") ? (
              <button className="btn-expired" disabled>
                <FaTimes /> Expired
              </button>
            ) : (
              <a className="btn-pay" onClick={() => handlePayNow(bid)}>
                <FaCreditCard /> Pay Now
              </a>
            )
          ) : bid.status === "Lost" ? (
            <button className="btn-lost" disabled>
              <FaTimes /> Lost
            </button>
          ) : bid.status === "Active" ? (
            <a className="btn-cancel" onClick={() => openCancelModal(bid)}>
              <FaTimes />
            </a>
          ) : (
            <button
              className="btn-view"
              onClick={() => navigate(`/buyer/details/${bid.content?._id}`)}
            >
              <FiEye />
            </button>
          )}
        </div>
      ),
    },
  ];

  return (
    <SectionWrapper
      icon={<FaGavel className="BuyerSidebar__icon" />}
      title="My Bids"
    >
      <div className="BuyerBids">
        {isLoading && !userBids?.length ? (
          <LoadingSkeleton />
        ) : isError ? (
          <ErrorDisplay
            error={error}
            onRetry={handleRetry}
            message="Failed to load bids"
          />
        ) : !userBids?.length ? (
          <div className="BuyerBids__empty">
            <FaGavel />
            <h3>No Bids Yet</h3>
            <p>You haven't placed any bids yet.</p>
          </div>
        ) : (
          <>
            <Table
              data={userBids}
              columns={columns}
              isLoading={isLoading}
              LoadingComponent={TableRowSkeleton}
            />
            {pagination && (
              <div className="BuyerBids__pagination">
                <Pagination
                  currentPage={currentPage}
                  totalPages={pagination.totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}

        {/* Cancel Bid Modal */}
        {showCancelModal && selectedBid && (
          <div className="cancel-modal-overlay" onClick={closeCancelModal}>
            <div className="cancel-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Cancel Bid</h3>
                <button className="close-btn" onClick={closeCancelModal}>
                  <FaTimes />
                </button>
              </div>
              <div className="modal-content">
                <div className="bid-details">
                  <h4>Bid Details</h4>
                  <p>
                    <strong>Content:</strong> {selectedBid.content?.title}
                  </p>
                  <p>
                    <strong>Bid Amount:</strong> $
                    {selectedBid.amount?.toFixed(2)}
                  </p>
                  <p>
                    <strong>Date:</strong> {formatDate(selectedBid.createdAt)}
                  </p>
                </div>
                <div className="warning-message">
                  <p>
                    Are you sure you want to cancel this bid? This action cannot
                    be undone. If you have a previous bid on this content, it
                    may be reactivated.
                  </p>
                </div>
                <div className="modal-actions">
                  <button
                    className="keepbid"
                    onClick={closeCancelModal}
                    disabled={isCancelling}
                  >
                    No, Keep Bid
                  </button>
                  <button
                    className="btn-primary"
                    onClick={() => handleCancelBid(selectedBid)}
                    disabled={isCancelling}
                  >
                    {isCancelling ? (
                      <>
                        <FaSync className="spin" /> Cancelling...
                      </>
                    ) : (
                      "Yes, Cancel Bid"
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </SectionWrapper>
  );
};

export default BuyerBids;
