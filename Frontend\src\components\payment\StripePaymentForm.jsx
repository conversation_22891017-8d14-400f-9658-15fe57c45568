import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createPaymentIntent, confirmPayment, clearPaymentIntent } from '../../redux/slices/paymentSlice';
import { fetchCards } from '../../redux/slices/cardSlice';
import { toast } from 'react-toastify';
import stripePromise from '../../utils/stripe';
import { FaCreditCard, FaPlus } from 'react-icons/fa';
import '../../styles/StripePaymentForm.css';

const StripePaymentForm = ({ order, onSuccess, onError, onCancel }) => {
  const dispatch = useDispatch();
  const stripeRef = useRef(null);
  const cardElementRef = useRef(null);
  const cardElementInstanceRef = useRef(null);

  const { paymentIntent, isLoading } = useSelector((state) => state.payment);
  const { cards } = useSelector((state) => state.cards);

  const [processing, setProcessing] = useState(false);
  const [cardComplete, setCardComplete] = useState(false);
  const [cardError, setCardError] = useState(null);
  const [stripeLoaded, setStripeLoaded] = useState(false);
  const [cardElementLoading, setCardElementLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('new'); // 'new' or 'saved'
  const [selectedCardId, setSelectedCardId] = useState('');
  const [billingDetails, setBillingDetails] = useState({
    name: '',
    email: '',
  });

  // Initialize Stripe and create card element
  useEffect(() => {
    const initializeStripe = async () => {
      try {
        const stripe = await stripePromise;
        if (!stripe) {
          throw new Error('Stripe failed to load');
        }

        stripeRef.current = stripe;
        setStripeLoaded(true);
      } catch (_error) {
        console.error('Failed to load payment form. Please refresh the page.');
      }
    };

    initializeStripe();
  }, []);

  // Create and mount card element when needed
  useEffect(() => {
    const createCardElement = async () => {
      // Only create if we need it and stripe is ready
      if ((paymentMethod === 'new' || !cards || cards.length === 0) && stripeRef.current) {
        setCardElementLoading(true);

        // Clean up existing element first
        if (cardElementInstanceRef.current) {
          try {
            cardElementInstanceRef.current.destroy();
          } catch (_error) {
            console.error('Card element already destroyed');
          }
          cardElementInstanceRef.current = null;
        }

        try {
          const elements = stripeRef.current.elements();
          const cardElement = elements.create('card', {
            style: {
              base: {
                color: '#424770',
                fontFamily: 'inherit',
                fontSmoothing: 'antialiased',
                fontSize: '16px',
                '::placeholder': {
                  color: '#aab7c4',
                },
              },
              invalid: {
                color: '#dc2626',
                iconColor: '#dc2626',
              },
              complete: {
                color: '#16a34a',
                iconColor: '#16a34a',
              },
            },
            hidePostalCode: true,
          });

          cardElementInstanceRef.current = cardElement;

          // Wait for DOM and mount
          setTimeout(() => {
            if (cardElementRef.current && cardElementInstanceRef.current) {
              try {
                cardElement.mount(cardElementRef.current);

                cardElement.on('change', (event) => {
                  setCardComplete(event.complete);
                  setCardError(event.error ? event.error.message : null);
                });

                cardElement.on('ready', () => {
                  setCardElementLoading(false);
                });

                cardElement.on('focus', () => {
                  setCardError(null);
                });
              } catch (mountError) {
                console.error('Error mounting card element:', mountError);
                setCardError('Failed to load payment form. Please refresh the page.');
                setCardElementLoading(false);
              }
            }
          }, 200);
        } catch (_error) {
          console.error('Error creating card element:', _error);
          setCardError('Failed to initialize payment form. Please refresh the page.');
          setCardElementLoading(false);
        }
      } else {
        setCardElementLoading(false);
      }
    };

    if (stripeLoaded) {
      createCardElement();
    }

    return () => {
      if (cardElementInstanceRef.current) {
        try {
          cardElementInstanceRef.current.destroy();
        } catch (_error) {
          console.error('Cleanup - card element already destroyed');
        }
        cardElementInstanceRef.current = null;
      }
    };
  }, [paymentMethod, cards, stripeLoaded]);

  // Fetch saved cards when component mounts
  useEffect(() => {
    dispatch(fetchCards());
  }, [dispatch]);

  // Set default payment method when cards are loaded
  useEffect(() => {
    if (cards && cards.length > 0) {
      const defaultCard = cards.find(card => card.isDefault);
      if (defaultCard) {
        setPaymentMethod('saved');
        setSelectedCardId(defaultCard._id);
      }
    } else if (cards && cards.length === 0) {
      // No saved cards - set payment method to 'new'
      setPaymentMethod('new');
      setSelectedCardId('');
    }
  }, [cards]);

  // Auto-populate name when payment method or selected card changes
  useEffect(() => {
    if (paymentMethod === 'saved' && selectedCardId && cards) {
      const selectedCard = cards.find(card => card._id === selectedCardId);
      if (selectedCard && selectedCard.cardholderName) {
        setBillingDetails(prev => ({
          ...prev,
          name: selectedCard.cardholderName
        }));
      }
    } else if (paymentMethod === 'new') {
      // Clear name when switching to new card to allow manual entry
      setBillingDetails(prev => ({
        ...prev,
        name: ''
      }));
    }
  }, [paymentMethod, selectedCardId, cards]);

  // Clear any existing payment intent when component mounts
  useEffect(() => {
    if (order && order._id) {
      dispatch(clearPaymentIntent());
    }
  }, [dispatch, order?._id]);

  // Create payment intent when component mounts
  useEffect(() => {
    // Only create payment intent if we don't already have one for this order
    if (order && order._id && !paymentIntent) {
      dispatch(createPaymentIntent({ orderId: order._id }))
        .unwrap()
        .then(() => {
          // Payment intent created successfully
        })
        .catch((_error) => {
          console.error('Error creating payment intent:', _error);
        });
    }
  }, [dispatch, order?._id, paymentIntent]);

  const handleBillingDetailsChange = (e) => {
    setBillingDetails({
      ...billingDetails,
      [e.target.name]: e.target.value,
    });
  };

  // Handle name input change for new cards
  const handleNameChange = (e) => {
    setBillingDetails({
      ...billingDetails,
      name: e.target.value,
    });
  };

  // Get cardholder name based on payment method
  const getCardholderName = () => {
    if (paymentMethod === 'saved' && selectedCardId) {
      const selectedCard = cards.find(card => card._id === selectedCardId);
      return selectedCard?.cardholderName || '';
    }
    return billingDetails.name;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripeRef.current || !paymentIntent) {
      setCardError('Payment system not ready. Please try again.');
      return;
    }

    // Validation based on payment method
    if (paymentMethod === 'new') {
      if (!cardElementInstanceRef.current) {
        setCardError('Payment system not ready. Please try again.');
        return;
      }
      if (!cardComplete) {
        setCardError('Please complete your card information');
        return;
      }
    } else if (paymentMethod === 'saved') {
      if (!selectedCardId) {
        setCardError('Please select a payment method');
        return;
      }
    }

    // Get the appropriate cardholder name
    const cardholderName = getCardholderName();
    if (!cardholderName.trim()) {
      toast.error('Please enter the cardholder name');
      return;
    }

    setProcessing(true);
    setCardError(null);

    try {
      let confirmedPaymentIntent;

      if (paymentMethod === 'saved') {
        // Find the selected card
        const selectedCard = cards.find(card => card._id === selectedCardId);
        if (!selectedCard) {
          throw new Error('Selected payment method not found');
        }

        // Confirm payment with saved payment method
        const { error: stripeError, paymentIntent: confirmedPI } = await stripeRef.current.confirmCardPayment(
          paymentIntent.clientSecret,
          {
            payment_method: selectedCard.stripePaymentMethodId,
          }
        );

        if (stripeError) {
          throw new Error(stripeError.message);
        }
        confirmedPaymentIntent = confirmedPI;
      } else {
        // Confirm payment with new card
        const { error: stripeError, paymentIntent: confirmedPI } = await stripeRef.current.confirmCardPayment(
          paymentIntent.clientSecret,
          {
            payment_method: {
              card: cardElementInstanceRef.current,
              billing_details: {
                name: cardholderName,
                email: billingDetails.email,
              },
            },
          }
        );

        if (stripeError) {
          throw new Error(stripeError.message);
        }
        confirmedPaymentIntent = confirmedPI;
      }

      if (confirmedPaymentIntent.status === 'succeeded') {
        // Confirm payment with backend
        const confirmResult = await dispatch(confirmPayment({
          orderId: order._id,
          paymentIntentId: confirmedPaymentIntent.id,
        })).unwrap();

        toast.success('Payment successful!');
        setProcessing(false);

        if (onSuccess) {
          onSuccess(confirmResult);
        }
      }
    } catch (err) {
      setCardError(err.message || 'An error occurred while processing your payment');
      setProcessing(false);

      if (onError) {
        onError(err);
      }
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  if (!order) {
    return (
      <div className="stripe-payment-form">
        <div className="payment-error">
          No order information available. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="stripe-payment-container">
      <div className="stripe-payment-form">

        <form onSubmit={handleSubmit} className="payment-form">
          {/* Payment Header - Always show */}
          <div className="payment-header">
            <h3>Payment Method</h3>
            <p>Complete your purchase securely with Stripe</p>
          </div>

          {/* Payment Method Selection - Show when user has saved cards */}
          {cards && cards.length > 0 ? (
            <div className="payment-method-selection">
              {/* Saved Cards */}
              <div className="saved-cards-section">
                <div className="payment-option">
                  <input
                    type="radio"
                    id="saved-card"
                    name="paymentMethod"
                    value="saved"
                    checked={paymentMethod === 'saved'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                  />
                  <label htmlFor="saved-card">Use saved card</label>
                </div>

                {paymentMethod === 'saved' && (
                  <div className="saved-cards-list">
                    {cards.map((card) => (
                      <div key={card._id} className="saved-card-item">
                        <input
                          type="radio"
                          id={`card-${card._id}`}
                          name="selectedCard"
                          value={card._id}
                          checked={selectedCardId === card._id}
                          onChange={(e) => setSelectedCardId(e.target.value)}
                        />
                        <label htmlFor={`card-${card._id}`} className="card-label">
                          <div className="card-info">
                            <FaCreditCard className="card-icon" />
                            <div className="card-details">
                              <div className="cards-details-style">
                                <span className="card-number">**** **** **** {card.lastFourDigits}</span>
                                <div className='gap-10 flex'>
                                  <span className="card-type">{card.cardType?.toUpperCase()}</span>
                                  <span className="card-expiry">{card.expiryMonth}/{card.expiryYear}</span>
                                </div>
                              </div>
                              {card.isDefault && <span className="default-badge">⭐</span>}
                            </div>
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* New Card Option */}
              <div className="payment-option">
                <input
                  type="radio"
                  id="new-card"
                  name="paymentMethod"
                  value="new"
                  checked={paymentMethod === 'new'}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                />
                <label htmlFor="new-card">
                  <FaPlus className="plus-icon" />
                  Add new card
                </label>
              </div>
            </div>
          ) : (
            /* No Saved Cards - Show message and set payment method to new */
            <></>
          )}

          {/* Card Element - Show for new card OR when no saved cards */}
          {(paymentMethod === 'new' || !cards || cards.length === 0) && (
            <div className="form-group">
              <label>Card Details*</label>

              <input
                type="text"
                name="name"
                value={billingDetails.name}
                onChange={handleNameChange}
                placeholder="Name on card"
                required
                className='card-element-container'
              />

              <div className="card-element-container">
                {cardElementLoading && (
                  <div className="card-element-loading">
                    <span className="spinner"></span>
                    <span>Loading payment form...</span>
                  </div>
                )}
                <div ref={cardElementRef} className="stripe-card-element" />
              </div>
            </div>
          )}

          {/* Billing Details */}
          <div className="billing-details">


            <div className="form-group">
              <label htmlFor="email">Email (optional)</label>
              <input
                type="email"
                id="email"
                name="email"
                value={billingDetails.email}
                onChange={handleBillingDetailsChange}
                placeholder="Enter email for receipt"
                className="form-input"
              />
            </div>
          </div>



          {/* Show error for any payment method */}
          {/* {cardError && (
            <div className="card-error">{cardError}</div>
          )} */}

          {/* Order Summary */}
          <div className="order-summary-payment">
            <div className="summary-row">
              <span>Content Price:</span>
              <span>${(order.amount || 0).toFixed(2)}</span>
            </div>
            {/* {order.platformFee > 0 && (
              <div className="summary-row platform-fee-info">
                <span>Platform Fee (included):</span>
                <span>${order.platformFee?.toFixed(2)}</span>
              </div>
            )} */}
            <div className="summary-row total">
              <span>Total:</span>
              <span>${(order.amount || 0).toFixed(2)}</span>
            </div>
            {order.platformFee > 0 && (
              <div className="fee-explanation">
                <small>Platform fee is deducted from seller earnings</small>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="payment-actions">
            <button
              type="button"
              onClick={handleCancel}
              className="btn-secondary cancel-btn"
              disabled={processing}
            >
              Cancel
            </button>

            <button
              type="submit"
              disabled={
                !stripeLoaded ||
                processing ||
                isLoading ||
                (paymentMethod === 'new' && !cardComplete) ||
                (paymentMethod === 'saved' && !selectedCardId)
              }
              className={`btn-primary pay-btn ${processing ? 'processing' : ''}`}
            >
              {processing || isLoading ? (
                <>
                  <span className="spinner"></span>
                  Processing...
                </>
              ) : (
                `Pay $${(order.amount || 0).toFixed(2)}`
              )}
            </button>
          </div>
        </form>

        {/* Security Notice */}
        <div className="security-notice">
          <p>🔒 Your payment information is secure and encrypted</p>
        </div>
      </div>
    </div>
  );
};

export default StripePaymentForm;
