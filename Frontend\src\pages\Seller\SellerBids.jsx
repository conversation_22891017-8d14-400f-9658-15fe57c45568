import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import {
  getSellerBids,
  updateBidStatus,
  setPage,
} from "../../redux/slices/bidSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import AuctionBidAcceptanceModal from "../../components/seller/AuctionBidAcceptanceModal";
import { FiEye } from "react-icons/fi";
import { FaGavel, FaCheck, FaTimes, FaClock, FaSync } from "react-icons/fa";
import Table from "../../components/common/Table";
import Pagination from "../../components/common/Pagination";
import "../../styles/SellerBids.css";
import { getImageUrl, IMAGE_BASE_URL } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";
import { formatWithTimezone, toLocal } from "../../utils/timezoneUtils";

const SellerBids = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sellerBids, isLoading, isError, error, pagination } = useSelector(
    (state) => state.bid
  );

  const [selectedBid, setSelectedBid] = useState(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    dispatch(getSellerBids({ page: pagination.page, limit: pagination.limit }));
  }, [dispatch, pagination.page]);

  const handleViewDetails = (bidId) => {
    navigate(`/seller/bid-details/${bidId.replace("#", "")}`);
  };

  const openBidModal = (bid) => {
    setSelectedBid(bid);
    setShowModal(true);
  };

  const closeBidModal = () => {
    setShowModal(false);
    setSelectedBid(null);
  };

  const handleBidAccepted = () => {
    // Refresh bids after acceptance/rejection
    dispatch(getSellerBids({ page: pagination.page, limit: pagination.limit }));
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "Active":
        return "status-active";
      case "Won":
        return "status-won";
      case "Lost":
        return "status-lost";
      case "Cancelled":
        return "status-cancelled";
      default:
        return "status-default";
    }
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const getAuctionStatus = (content) => {
    if (!content?.auctionDetails) return "Not an auction";

    const now = new Date();
    const startDate = content.auctionDetails.auctionStartDate
      ? toLocal(new Date(content.auctionDetails.auctionStartDate))
      : null;
    const endDate = content.auctionDetails.auctionEndDate
      ? toLocal(new Date(content.auctionDetails.auctionEndDate))
      : null;

    if (startDate && now < startDate) {
      return "Upcoming";
    }

    if (startDate && endDate && now >= startDate && now <= endDate) {
      return "Active";
    }

    if (endDate && now > endDate) {
      return "Ended";
    }

    return "Unknown";
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    { key: "bidId", label: "Bid Id" },
    {
      key: "content",
      label: "Videos/Documents",
      render: (bid) => (
        <div className="video-doc">
          <img
            src={getImageUrl(bid.content?.thumbnailUrl) ||
              "https://via.placeholder.com/60x40"
            }
            alt={bid.content?.title || "Content"}
          />
          <div className="content-details">
            <span className="content-title">
              {bid.content?.title || "Unknown Content"}
            </span>
            <span className="content-type">
              {bid.content?.contentType || "Unknown"}
            </span>
          </div>
        </div>
      ),
    },
    {
      key: "bidder",
      label: "Bidder",
      render: (bid) => (
        <div className="bidder-info">
          <span className="bidder-name">
            {bid.bidder?.firstName} {bid.bidder?.lastName}
          </span>
        </div>
      ),
    },
    {
      key: "date",
      label: "Date",
      render: (bid) => formatDate(bid.createdAt),
    },
    {
      key: "bidAmount",
      label: "Bid Amount",
      render: (bid) => `$${bid.amount?.toFixed(2) || "0.00"}`,
    },
    {
      key: "status",
      label: "Status",
      render: (bid) => (
        <span className={`status-badge ${getStatusBadgeClass(bid.status)}`}>
          {bid.status}
        </span>
      ),
    },
    {
      key: "auctionStatus",
      label: "Auction",
      render: (bid) => (
        <div className="auction-details">
          <p>
            Start:{" "}
            {bid.content?.auctionDetails?.auctionStartDate
              ? formatWithTimezone(bid.content.auctionDetails.auctionStartDate)
              : "Not set"}
          </p>
          <p>
            End:{" "}
            {bid.content?.auctionDetails?.auctionEndDate
              ? formatWithTimezone(bid.content.auctionDetails.auctionEndDate)
              : "Not set"}
          </p>
        </div>
      ),
    },
    {
      key: "action",
      label: "Action",
      render: (bid) => (
        <div className="action-buttons">
          {bid.status === "Active" ? (
            <button className="btn-review" onClick={() => openBidModal(bid)}>
              <FaGavel />
            </button>
          ) : (
            <button
              className="btn-view"
              onClick={() => handleViewDetails(bid._id)}
            >
              <FiEye />
            </button>
          )}
        </div>
      ),
    },
  ];

  const formatData = (bids) => {
    return bids.map((bid, index) => ({
      ...bid,
      no: index + 1,
      bidId: `#${bid._id?.slice(-6) || "N/A"}`,
    }));
  };

  const handlePageChange = (newPage) => {
    dispatch(setPage(newPage));
  };

  if (isLoading) {
    return (
      <SellerLayout>
        <div className="video-status-container">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading bids...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  if (isError) {
    return (
      <SellerLayout>
        <ErrorDisplay error={error} />
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="seller-bids-container">
        <div>
          <Table
            columns={columns}
            data={formatData(sellerBids)}
            emptyMessage="No bids found"
          />
          {pagination.totalPages > 1 && (
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      </div>

      {/* Auction Bid Acceptance Modal */}
      <AuctionBidAcceptanceModal
        isOpen={showModal}
        onClose={closeBidModal}
        bid={selectedBid}
        content={selectedBid?.content}
        onBidAccepted={handleBidAccepted}
      />
    </SellerLayout>
  );
};

export default SellerBids;
